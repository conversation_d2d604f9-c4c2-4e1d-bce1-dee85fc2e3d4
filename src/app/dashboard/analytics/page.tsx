"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { DateRangeParams, PomodoroStatsResponse, useGetPomodoroStats } from "../../../../prisma/schema/Pomodoro/pomodoro-query"
import { useCoordinatedSync } from "../../../hooks/useCoordinatedSync"
import { WelcomeHeader } from "../_components/welcome-header"
import { MetricCards } from "../_components/metric-cards"
import { AnalyticsOverviewTab } from "../_components/analytics-overview-tab"
// import { AIInsightsTab } from "../_components/ai-insights-tab"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface StatsDataType {
  dailyFocusTime: Array<{ date: string; minutes: number }>
  completionRate: { completed: number; interrupted: number }
  hourlyDistribution: Array<{ hour: string; value: number }>
  weeklyComparison: Array<{ name: string; thisWeek: number; lastWeek: number }>
  dailyHeatmap: Array<{ day: string; hour: number; value: number }>
  todaySessions: Array<{
    title?: string
    timeRange: string
    duration: number
    completed: boolean
  }>
  todayQuality: number
  recommendation: string
  streakData: Array<{ date: string; count: number }>
  longestStreak: number
  daysActiveThisMonth: number
  daysActiveLastMonth: number
  consistencyScore: number
  monthlyProgress: Array<{ month: string; totalHours: number }>
  monthOverMonthGrowth: number
  dailyCalendarSessions: Array<{
    id: string
    title: string
    type: "focus" | "shortBreak" | "longBreak"
    startTime: string
    endTime: string
    completed: boolean
  }>
  productivityScore: number
  focusEfficiency: number
  deepWorkRatio: number
  goalAchievement: number
  flowStateRating: string
  flowStateTime: string
  flowStateDuration: string
  flowStateData: Array<{ time: string; flowScore: number }>
  flowTriggers: string[]
  flowBlockers: string[]
  optimalTimeData: Array<{ hour: string; score: number }>
  optimalDay: string
  optimalDayCompletion: number
  optimalTime: string
  optimalTimeEfficiency: number
  optimalDuration: string
  optimalDurationSuccess: number
  productivityRhythm: string
  focusDurationInsight: string
  consistencyPattern: string
  recommendations: string[]
}

export default function AnalyticsPage() {
  const [activeTab, setActiveTab] = useState("analytics-overview")
  const [specificDate] = useState<string | undefined>(undefined)
  const [dateRange] = useState<DateRangeParams | undefined>(undefined)

  const { syncStatus } = useCoordinatedSync()

  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  const yesterdayStr = yesterday.toISOString().split('T')[0]

  const { data: previousStatsData } = useGetPomodoroStats(
    undefined,
    yesterdayStr,
    undefined
  )

  const { data: statsData, isLoading, error } = useGetPomodoroStats(
    30,
    specificDate,
    dateRange
  )

  const safeStatsData = statsData || {
    totalSessions: 0,
    completedSessions: 0,
    interruptedSessions: 0,
    totalDuration: 0,
    completionRate: 0,
    focusSessions: 0,
    shortBreakSessions: 0,
    longBreakSessions: 0,
    focusDuration: 0,
    shortBreakDuration: 0,
    longBreakDuration: 0,
    sessionsByDay: {},
    dateRange: {
      startDate: new Date().toISOString(),
      endDate: new Date().toISOString()
    }
  }

  const transformedData: StatsDataType | null = statsData ? transformApiDataToUiFormat(statsData) : null

  const todayFocusTimeSeconds = statsData ?
    statsData.todaySessions
      .filter(session => session.type === 'focus')
      .reduce((sum, session) => sum + (session.duration * 60), 0)
    : 0

  const todayCompletedFocusSessions = statsData ?
    statsData.todaySessions
      .filter(session => session.type === 'focus' && session.completed)
      .length
    : 0

  const yesterdayFocusTimeSeconds = previousStatsData ?
    previousStatsData.todaySessions
      .filter(session => session.type === 'focus')
      .reduce((sum, session) => sum + (session.duration * 60), 0)
    : 0

  const yesterdayCompletedFocusSessions = previousStatsData ?
    previousStatsData.todaySessions
      .filter(session => session.type === 'focus' && session.completed)
      .length
    : 0

  function transformApiDataToUiFormat(apiData: PomodoroStatsResponse): StatsDataType {
    const hourLabels = [
      "12 AM", "1 AM", "2 AM", "3 AM", "4 AM", "5 AM",
      "6 AM", "7 AM", "8 AM", "9 AM", "10 AM", "11 AM",
      "12 PM", "1 PM", "2 PM", "3 PM", "4 PM", "5 PM",
      "6 PM", "7 PM", "8 PM", "9 PM", "10 PM", "11 PM"
    ];

    const dailyFocusTime = Object.entries(apiData.sessionsByDay || {}).map(([date, data]) => ({
      date,
      minutes: data.focusMinutes
    }));

    const todaySessions = (apiData.todaySessions || []).map(session => ({
      title: session.title || `${session.type} Session`,
      timeRange: `${session.startTime} - ${session.endTime}`,
      duration: session.duration,
      completed: session.completed
    }));

    const dailyCalendarSessions = (apiData.todaySessions || []).map((session, index) => ({
      id: `session-${index}`,
      title: session.title || `${session.type} Session`,
      type: session.type as "focus" | "shortBreak" | "longBreak",
      startTime: session.startTime,
      endTime: session.endTime,
      completed: session.completed
    }));

    const streakData = dailyFocusTime
      .filter(day => day.minutes > 0)
      .map(day => ({ date: day.date, count: 1 }));

    const totalFocusMinutes = dailyFocusTime.reduce((sum, day) => sum + day.minutes, 0);
    const productivityScore = Math.min(Math.round((totalFocusMinutes / (dailyFocusTime.length * 120)) * 100), 100);
    const focusEfficiency = Math.round(apiData.completionRate);
    const deepWorkRatio = Math.round((apiData.focusDuration / apiData.totalDuration) * 100);

    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    const daysActiveThisMonth = Math.min(streakData.length, daysInMonth);
    const daysActiveLastMonth = Math.floor(daysActiveThisMonth * 0.8);
    const totalDaysInRange = Math.max(dailyFocusTime.length, 1);

    return {
      dailyFocusTime,
      completionRate: {
        completed: apiData.completedSessions,
        interrupted: apiData.interruptedSessions
      },
      hourlyDistribution: apiData.hourlyDistribution.map(hour => ({
        hour: hourLabels[hour.hour],
        value: hour.value
      })),
      weeklyComparison: apiData.weeklyComparison,
      dailyHeatmap: [],
      todaySessions,
      todayQuality: Math.round(apiData.completionRate),
      recommendation: apiData.completionRate > 80 ? "Excellent focus today!" : "Room for improvement",
      streakData,
      longestStreak: Math.max(...streakData.map(s => s.count), 0),
      daysActiveThisMonth,
      daysActiveLastMonth,
      consistencyScore: Math.round((daysActiveThisMonth / totalDaysInRange) * 100),
      monthlyProgress: [],
      monthOverMonthGrowth: 0,
      dailyCalendarSessions,
      productivityScore,
      focusEfficiency,
      deepWorkRatio,
      goalAchievement: Math.round(apiData.completionRate),
      flowStateRating: apiData.completionRate > 80 ? "Excellent" : apiData.completionRate > 60 ? "Good" : "Developing",
      flowStateTime: getFlowStateTime(apiData.hourlyDistribution),
      flowStateDuration: "25",
      flowStateData: [],
      flowTriggers: ["Background music", "Clear goals", "Minimal distractions"],
      flowBlockers: ["Notifications", "Multitasking", "Unclear objectives"],
      optimalTimeData: apiData.hourlyDistribution
        .filter(hour => hour.value > 0)
        .map(hour => ({
          hour: hourLabels[hour.hour],
          score: Math.min(hour.value * 2, 100)
        })),
      optimalDay: getOptimalDay(apiData.weeklyComparison),
      optimalDayCompletion: Math.round(apiData.completionRate),
      optimalTime: getOptimalTimeRange(apiData.hourlyDistribution),
      optimalTimeEfficiency: focusEfficiency,
      optimalDuration: "25 min",
      optimalDurationSuccess: Math.round(apiData.completionRate),
      productivityRhythm: `Your productivity pattern appears to be strongest in the ${getFlowStateTime(apiData.hourlyDistribution)} based on completion rates.`,
      focusDurationInsight: `Your optimal focus session length appears to be 25 minutes, with a completion rate of ${Math.round(apiData.completionRate)}%.`,
      consistencyPattern: streakData.length > 10 ?
        "You show consistent focus sessions throughout the date range." :
        "Your focus sessions are somewhat irregular. Consider setting a regular schedule.",
      recommendations: [
        "Try to maintain a regular schedule for your Pomodoro sessions",
        "Consider shorter breaks between sessions to maintain momentum",
        "Focus on completing sessions without interruption",
        "Track tasks associated with your Pomodoro sessions for better productivity insights"
      ]
    };
  }

  function getFlowStateTime(hourlyData: Array<{ hour: number; value: number }>): string {
    const morning = hourlyData.filter(h => h.hour >= 6 && h.hour < 12).reduce((sum, h) => sum + h.value, 0);
    const afternoon = hourlyData.filter(h => h.hour >= 12 && h.hour < 18).reduce((sum, h) => sum + h.value, 0);
    const evening = hourlyData.filter(h => h.hour >= 18 || h.hour < 6).reduce((sum, h) => sum + h.value, 0);

    if (morning >= afternoon && morning >= evening) return "morning";
    if (afternoon >= morning && afternoon >= evening) return "afternoon";
    return "evening";
  }

  function getOptimalDay(weeklyData: Array<{ name: string; thisWeek: number; lastWeek: number }>): string {
    let maxDay = weeklyData[0];
    weeklyData.forEach(day => {
      if (day.thisWeek > maxDay.thisWeek) {
        maxDay = day;
      }
    });
    return maxDay.name;
  }

  function getOptimalTimeRange(hourlyData: Array<{ hour: number; value: number }>): string {
    let maxHour = { hour: 0, value: 0 };
    hourlyData.forEach(hour => {
      if (hour.value > maxHour.value) {
        maxHour = hour;
      }
    });

    const hourLabels = [
      "12 AM", "1 AM", "2 AM", "3 AM", "4 AM", "5 AM",
      "6 AM", "7 AM", "8 AM", "9 AM", "10 AM", "11 AM",
      "12 PM", "1 PM", "2 PM", "3 PM", "4 PM", "5 PM",
      "6 PM", "7 PM", "8 PM", "9 PM", "10 PM", "11 PM"
    ];

    if (maxHour.value === 0) return "10-12 AM";

    const startHour = maxHour.hour;
    const endHour = (startHour + 2) % 24;

    return `${hourLabels[startHour]}-${hourLabels[endHour]}`;
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background dark:bg-background">
        <div className="text-center space-y-4">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-muted border-t-primary mx-auto"></div>
          <h3 className="text-lg font-medium">Loading Analytics</h3>
          <p className="text-muted-foreground">Please wait while we fetch your data</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-background dark:bg-background">
        <h1 className="text-xl font-bold">Error loading stats</h1>
        <p className="text-muted-foreground">{error instanceof Error ? error.message : "Failed to load statistics"}</p>
        <Button
          variant="outline"
          className="mt-4 focus-ring"
          onClick={() => window.location.reload()}
        >
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className="flex-1 overflow-y-auto overflow-x-hidden">
      <div className="w-full max-w-[90rem] mx-auto p-4 overflow-hidden">
        <WelcomeHeader />

        {transformedData && (
          <MetricCards
            focusTimeToday={todayFocusTimeSeconds}
            completedSessions={todayCompletedFocusSessions}
            currentStreak={transformedData.longestStreak || 0}
            totalFocusTime={safeStatsData.focusDuration}
            previousFocusTime={yesterdayFocusTimeSeconds}
            previousCompletedSessions={yesterdayCompletedFocusSessions}
            previousStreak={0}
            previousTotalFocusTime={previousStatsData?.focusDuration || 0}
          />
        )}

        {transformedData && (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-1">
              <TabsTrigger value="analytics-overview">Overview</TabsTrigger>
              {/* <TabsTrigger value="ai-insights">AI Insights</TabsTrigger> */}
            </TabsList>
            
            <TabsContent value="analytics-overview" className="mt-6">
              <AnalyticsOverviewTab transformedData={transformedData} safeStatsData={safeStatsData} />
            </TabsContent>

            {/* <TabsContent value="ai-insights" className="mt-6">
              <AIInsightsTab transformedData={transformedData} />
            </TabsContent> */}
          </Tabs>
        )}
      </div>
    </div>
  )
}